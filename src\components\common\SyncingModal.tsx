import React, { useEffect, useState } from "react";
import <PERSON>actD<PERSON> from "react-dom";

interface SyncingModalProps {
  open: boolean;
}

const SyncingModal: React.FC<SyncingModalProps> = ({ open }) => {
  const [container, setContainer] = useState<HTMLDivElement | null>(null);
  const [currentStep, setCurrentStep] = useState(0);

  const steps = [
    "Connecting to Google Calendar...",
    "Validating session data...",
    "Checking for conflicts...",
    "Syncing events...",
    "Finalizing sync..."
  ];

  useEffect(() => {
    if (typeof document !== "undefined") {
      const portalContainer = document.getElementById("__next");
      if (!portalContainer) {
        const newContainer = document.createElement("div");
        newContainer.id = "portal-root";
        document.body.appendChild(newContainer);
        setContainer(newContainer);
      } else {
        setContainer(portalContainer as HTMLDivElement);
      }
    }
  }, []);

  useEffect(() => {
    if (open) {
      document.body.style.overflow = "hidden";
      setCurrentStep(0);
      
      // Animate through steps
      const stepInterval = setInterval(() => {
        setCurrentStep(prev => {
          if (prev < steps.length - 1) {
            return prev + 1;
          }
          return prev;
        });
      }, 1200);

      return () => {
        clearInterval(stepInterval);
        document.body.style.overflow = "auto";
      };
    } else {
      document.body.style.overflow = "auto";
    }
  }, [open, steps.length]);

  if (!container) {
    return null;
  }

  return ReactDOM.createPortal(
    <div
      className={`fixed inset-0 z-[99999] flex items-center justify-center bg-gray-900 bg-opacity-75 transition-opacity duration-300 ${
        open ? "opacity-100" : "opacity-0 pointer-events-none"
      }`}
    >
      <div className="bg-white rounded-2xl shadow-2xl p-8 mx-4 w-full max-w-md transform transition-transform duration-300">
        <div className="text-center">
          {/* Animated sync icon */}
          <div className="relative mb-8">
            <div className="w-20 h-20 mx-auto">
              {/* Outer rotating ring */}
              <div className="absolute inset-0 border-4 border-blue-200 rounded-full animate-spin"></div>
              {/* Inner rotating ring */}
              <div className="absolute inset-2 border-4 border-blue-500 border-t-transparent rounded-full animate-spin" style={{ animationDirection: 'reverse', animationDuration: '1s' }}></div>
              {/* Center icon */}
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-3xl animate-pulse">🔄</div>
              </div>
            </div>
          </div>

          {/* Title */}
          <h2 className="text-2xl font-bold text-gray-800 mb-6">
            Syncing Your Events
          </h2>

          {/* Progress steps */}
          <div className="space-y-4 mb-8">
            {steps.map((step, index) => (
              <div
                key={index}
                className={`flex items-center space-x-3 transition-all duration-500 ${
                  index <= currentStep ? 'opacity-100' : 'opacity-30'
                }`}
              >
                <div
                  className={`w-4 h-4 rounded-full transition-all duration-500 ${
                    index < currentStep
                      ? 'bg-green-500 scale-110'
                      : index === currentStep
                      ? 'bg-blue-500 animate-pulse scale-110'
                      : 'bg-gray-300'
                  }`}
                ></div>
                <span
                  className={`text-sm transition-all duration-500 ${
                    index <= currentStep ? 'text-gray-700 font-medium' : 'text-gray-400'
                  }`}
                >
                  {step}
                </span>
                {index < currentStep && (
                  <div className="ml-auto text-green-500 animate-bounce">✓</div>
                )}
                {index === currentStep && (
                  <div className="ml-auto">
                    <div className="flex space-x-1">
                      <div className="w-1 h-1 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
                      <div className="w-1 h-1 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
                      <div className="w-1 h-1 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Progress bar */}
          <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
            <div
              className="bg-gradient-to-r from-blue-500 to-indigo-600 h-2 rounded-full transition-all duration-1000 ease-out"
              style={{ width: `${((currentStep + 1) / steps.length) * 100}%` }}
            ></div>
          </div>

          {/* Status text */}
          <p className="text-sm text-gray-600">
            Please wait while we sync your calendar events...
          </p>
        </div>
      </div>
    </div>,
    container
  );
};

export default SyncingModal;
