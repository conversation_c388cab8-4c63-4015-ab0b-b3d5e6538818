import React, { useEffect, useState } from "react";
import <PERSON>act<PERSON><PERSON> from "react-dom";
import Button from "./Button";
import { IoCloseCircleOutline } from "react-icons/io5";

interface RescheduledModalProps {
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
  loading?: boolean;
}

const RescheduledModal: React.FC<RescheduledModalProps> = ({
  open,
  onClose,
  onConfirm,
  loading = false,
}) => {
  const [container, setContainer] = useState<HTMLDivElement | null>(null);

  useEffect(() => {
    if (typeof document !== "undefined") {
      // Ensure that the container exists in the DOM
      const portalContainer = document.getElementById("__next");
      if (!portalContainer) {
        const newContainer = document.createElement("div");
        newContainer.id = "portal-root";
        document.body.appendChild(newContainer);
        setContainer(newContainer);
      } else {
        setContainer(portalContainer as HTMLDivElement);
      }
    }
  }, []);

  useEffect(() => {
    if (open) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "auto";
    }

    // Cleanup function to reset overflow when component unmounts
    return () => {
      document.body.style.overflow = "auto";
    };
  }, [open]);

  if (!container) {
    return null; // Don't render anything if container is not available
  }

  return ReactDOM.createPortal(
    <div
      className={`fixed inset-0 z-[99999] flex items-center justify-center bg-gray-800 bg-opacity-50 transition-opacity duration-300 ${
        open ? "opacity-100" : "opacity-0 pointer-events-none"
      }`}
      onClick={onClose}
    >
      <div
        className="bg-white rounded-md shadow-lg p-4 transform transition-transform duration-300 overflow-y-auto mx-4 my-6 sm:my-8 w-full md:w-3/4 lg:w-3/5 2xl:w-2/5 max-w-[90%] md:max-w-[75%] lg:max-w-[60%] 2xl:max-w-[40%] max-h-[90vh] sm:max-h-[85vh]"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Close button */}
        <button
          className="absolute top-3 right-3 text-gray-500 hover:text-gray-700 text-xl font-bold"
          onClick={onClose}
          aria-label="Close modal"
        >
          <IoCloseCircleOutline className="w-8 h-8" />
        </button>

        <div className="pt-8 pb-4 px-2 sm:px-4">
          <div className="p-5 text-center">
            {/* Sad emoji animation */}
            <div className="text-6xl mb-4 animate-bounce">
              😔
            </div>
            
            <h2 className="text-xl font-semibold mb-4 text-gray-800">
              Rescheduled Event Detected
            </h2>
            
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
              <p className="text-base text-gray-700 mb-4">
                This event has been rescheduled in Google Calendar. To maintain flexibility and avoid conflicts:
              </p>
              
              <div className="text-left space-y-2 mb-4">
                <p className="text-sm text-gray-600">
                  1. Please delete the rescheduled event from your Google Calendar
                </p>
                <p className="text-sm text-gray-600">
                  2. Return to our dashboard to reschedule the session properly
                </p>
                <p className="text-sm text-gray-600">
                  3. This ensures better coordination and prevents scheduling conflicts
                </p>
              </div>
            </div>
            
            <p className="text-sm text-gray-600 mb-6">
              We appreciate your cooperation in keeping everything synchronized! 🙏
            </p>
            
            <div className="flex justify-center">
              <Button
                onClick={onConfirm}
                className="px-6 py-2 text-white rounded-lg bg-yellow-600 hover:bg-yellow-700"
                disabled={loading}
              >
                {loading ? "Processing..." : "Got it, I'll handle this"}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>,
    container
  );
};

export default RescheduledModal;
