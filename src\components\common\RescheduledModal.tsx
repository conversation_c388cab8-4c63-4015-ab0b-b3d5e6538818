import React, { useEffect, useState } from "react";
import ReactD<PERSON> from "react-dom";
import { IoCloseCircleOutline } from "react-icons/io5";

interface RescheduledModalProps {
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
  loading?: boolean;
}

const RescheduledModal: React.FC<RescheduledModalProps> = ({
  open,
  onClose,
  onConfirm,
  loading = false,
}) => {
  const [container, setContainer] = useState<HTMLDivElement | null>(null);

  useEffect(() => {
    if (typeof document !== "undefined") {
      // Ensure that the container exists in the DOM
      const portalContainer = document.getElementById("__next");
      if (!portalContainer) {
        const newContainer = document.createElement("div");
        newContainer.id = "portal-root";
        document.body.appendChild(newContainer);
        setContainer(newContainer);
      } else {
        setContainer(portalContainer as HTMLDivElement);
      }
    }
  }, []);

  useEffect(() => {
    if (open) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "auto";
    }

    // Cleanup function to reset overflow when component unmounts
    return () => {
      document.body.style.overflow = "auto";
    };
  }, [open]);

  if (!container) {
    return null; // Don't render anything if container is not available
  }

  return ReactDOM.createPortal(
    <div
      className={`fixed inset-0 z-[99999] flex items-center justify-center bg-gray-800 bg-opacity-50 transition-opacity duration-300 ${
        open ? "opacity-100" : "opacity-0 pointer-events-none"
      }`}
      onClick={onClose}
    >
      <div
        className="bg-white rounded-xl shadow-2xl p-4 transform transition-transform duration-300 overflow-y-auto mx-4 my-6 sm:my-8 w-full md:w-4/5 lg:w-3/5 2xl:w-2/5 max-w-[95%] md:max-w-[80%] lg:max-w-[60%] 2xl:max-w-[40%] max-h-[90vh] sm:max-h-[85vh]"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Close button */}
        <button
          className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors duration-200 hover:bg-gray-100 rounded-full p-1"
          onClick={onClose}
          aria-label="Close modal"
        >
          <IoCloseCircleOutline className="w-8 h-8" />
        </button>

        <div className="pt-8 pb-4 px-2 sm:px-4">
          <div className="p-6 text-center">
            {/* Calendar emoji animation */}
            <div className="text-6xl mb-6 animate-pulse">
              🗓️
            </div>

            <h2 className="text-2xl font-bold mb-6 text-gray-800 leading-tight">
              Spotted a session rescheduled via Google Calendar!
            </h2>

            <div className="bg-gradient-to-br from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-6 mb-6 text-left">
              <p className="text-base text-gray-700 mb-4 leading-relaxed">
                We get it — G-Cal is quick. But it doesn&apos;t watch out for double bookings… therapists do.
              </p>

              <p className="text-base text-gray-700 mb-4 leading-relaxed">
                So we&apos;ve held off syncing some sessions to avoid any clashes.
              </p>

              <div className="bg-white rounded-lg p-4 border border-blue-100 mb-4">
                <p className="text-base font-medium text-blue-800 mb-2">
                  📋 What to do next:
                </p>
                <p className="text-sm text-gray-700">
                  Go ahead and reschedule it from your <span className="font-semibold text-blue-600">Thought Pudding Dashboard</span>.
                </p>
              </div>

              <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                <p className="text-sm text-green-700 font-medium">
                  ✅ P.S. All your other sessions are safely synced.
                </p>
              </div>
            </div>

            <div className="flex justify-center">
              <button
                onClick={onConfirm}
                disabled={loading}
                className="px-8 py-3 text-white rounded-lg bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 font-medium shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
              >
                {loading ? "Processing..." : "Got it"}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>,
    container
  );
};

export default RescheduledModal;
